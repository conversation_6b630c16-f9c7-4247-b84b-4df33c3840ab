import axios from 'axios';

// Create custom axios instance for Eldorado API
const eldoradoAxios = axios.create({
  baseURL: "/api/eldorado",
  timeout: 30000,
});

// Add request interceptor to handle cookies manually
eldoradoAxios.interceptors.request.use((config) => {
  // Remove the Cookie header from axios and let the proxy handle it
  const headers = getEldoradoAuthHeaders();
  const { Cookie, ...otherHeaders } = headers;

  // Set all headers except Cookie
  Object.assign(config.headers, otherHeaders);

  return config;
});

// Default API configuration for Eldorado (using Vite proxy)
const ELDORADO_API_CONFIG = {
  baseUrl: "/api/eldorado",
  defaultHeaders: {
    "Content-Type": "application/json",
  },
};




// Helper function to get auth headers for cookie-based authentication
const getEldoradoAuthHeaders = () => {
  const headers: Record<string, string> = {
    ...ELDORADO_API_CONFIG.defaultHeaders,
    // Add XSRF token for CSRF protection
    "X-XSRF-Token": "d0760bfcb9a38dfe2698590b35a5612e950e7354cf58d33d45e1afff2fe9253f",
    "X-Correlation-Id": "0ff1f794-6552-40ff-833c-f93812440910",
    "X-Device-Id": "b1419cba-fcd8-4b8f-aea5-34a05956c4bb",
    "X-Client-Build": "-Time: 2025-08-01_06:14:04",
    "X-GA-SessionId": "Unknown",

    // Add cookies manually
    "XX-Cookie": "",
    // Add additional headers for proxy
    "X-Requested-With": "XMLHttpRequest",
  };

  return headers;
};

// Interfaces for the flexible offers search API
interface TradeEnvironmentValue {
  value: string;
  name: string;
  id: string;
  imageLocation: string | null;
}

interface OfferAttributeIdValue {
  value: string;
  name: string;
  id: string;
  imageLocation: string | null;
}

interface OfferImage {
  smallImage: string;
  largeImage: string;
  originalSizeImage: string;
}

interface PriceAmount {
  amount: number;
  currency: string;
}

interface ExchangeRate {
  currency: string;
  exchangeRate: number;
}

interface FlexibleOffer {
  tradeEnvironmentValues: TradeEnvironmentValue[];
  offerAttributeIdValues: OfferAttributeIdValue[];
  offerTitle: string;
  offerTitleOriginal: string;
  mainOfferImage: OfferImage;
  offerImages: OfferImage[];
  tags: string[];
  id: string;
  userId: string;
  gameId: string;
  category: string;
  gameCategoryTitle: string;
  gameSeoAlias: string;
  description: string;
  quantity: number;
  minQuantity: number;
  volumeDiscounts: any[];
  pricePerUnit: PriceAmount;
  pricePerUnitWithDiscount: PriceAmount | null;
  discountPercentage: number | null;
  guaranteedDeliveryTime: string;
  expireDate: string;
  offerState: string;
  version: string;
  exchangeRate: ExchangeRate;
  pricePerUnitInUSD: PriceAmount;
}

export interface FlexibleOffersSearchResponse {
  pageIndex: number;
  totalPages: number;
  recordCount: number;
  pageSize: number;
  results: FlexibleOffer[];
}

// Parameters for listing current offers
export interface ListCurrentOffersParams {
  pageIndex?: number;
  pageSize?: number;
  category?: string;
}

/**
 * Eldorado offers service
 */
export const eldoradoOffersService = {
  /**
   * List current offers for the authenticated user
   *
   * @param pageIndex Current page index (default: 1)
   * @param pageSize Number of items per page (default: 40)
   * @param category Filter by category (default: "Account")
   * @returns Promise with the current offers data
   */
  listCurrentOffers: async ({
    pageIndex = 1,
    pageSize = 40,
    category = "Account",
  }: ListCurrentOffersParams = {}): Promise<FlexibleOffersSearchResponse> => {
    try {
      const response = await axios.get(`${ELDORADO_API_CONFIG.baseUrl}/flexibleOffers/me/search`, {
        params: {
          pageIndex: pageIndex.toString(),
          pageSize: pageSize.toString(),
          category,
        },
        headers: getEldoradoAuthHeaders(),
        withCredentials: false,
      });

      return response.data as FlexibleOffersSearchResponse;
    } catch (error) {
      console.error("Failed to fetch current Eldorado offers:", error);
      throw error;
    }
  },
};

export default {
  offers: eldoradoOffersService,
};