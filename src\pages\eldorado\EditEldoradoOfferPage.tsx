import React, { useState, useEffect, useMemo, useCallback } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ArrowLeft, Save, Plus, Trash2, Play } from "lucide-react";
import { EldoradoFillWithAIButton } from "@/components/EldoradoFillWithAIButton";
import { SelectLocalAccountDialog } from "@/components/SelectLocalAccountDialog";
import { EldoradoImageUploader } from "@/components/EldoradoImageUploader";
import { eldoradoOffersService, mapPrivateOfferToUpdateRequest, type OfferEditDetailsResponse, type OfferAccountDetailsResponse, type OfferImage } from "@/lib/eldorado-api";
import { LocalAccount } from "@/lib/db";
import { createEldoradoImageUrl } from "@/lib/utils";

// Memoized component to prevent excessive image re-requests
const OfferImages = React.memo(({ 
  mainImage, 
  images, 
  onDeleteMainImage, 
  onDeleteImage 
}: { 
  mainImage: OfferImage | null, 
  images: OfferImage[], 
  onDeleteMainImage?: () => void,
  onDeleteImage?: (index: number) => void
}) => {
  const getImageUrl = useCallback((imagePath: string) => {
    return createEldoradoImageUrl(imagePath);
  }, []);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Main Offer Image */}
         {mainImage && (
           <div className="space-y-2">
             <div className="flex items-center justify-between">
               <p className="text-sm font-medium text-muted-foreground">Main Image</p>
               {onDeleteMainImage && (
                 <Button
                   type="button"
                   variant="outline"
                   size="sm"
                   onClick={onDeleteMainImage}
                   className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                 >
                   <Trash2 className="h-3 w-3" />
                 </Button>
               )}
             </div>
             <div className="border rounded-lg overflow-hidden">
               <img
                 src={getImageUrl(mainImage.largeImage)}
                 alt="Main offer image"
                 className="w-full h-48 object-cover hover:scale-105 transition-transform duration-200"
                 loading="lazy"
               />
             </div>
           </div>
         )}
        {/* Additional Offer Images */}
         {images.map((image, index) => (
           <div key={`${image.largeImage}-${index}`} className="space-y-2">
             <div className="flex items-center justify-between">
               <p className="text-sm font-medium text-muted-foreground">Image {index + 1}</p>
               {onDeleteImage && (
                 <Button
                   type="button"
                   variant="outline"
                   size="sm"
                   onClick={() => onDeleteImage(index)}
                   className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                 >
                   <Trash2 className="h-3 w-3" />
                 </Button>
               )}
             </div>
             <div className="border rounded-lg overflow-hidden">
               <img
                 src={getImageUrl(image.largeImage)}
                 alt={`Offer image ${index + 1}`}
                 className="w-full h-48 object-cover hover:scale-105 transition-transform duration-200"
                 loading="lazy"
               />
             </div>
           </div>
         ))}
      </div>
    </div>
  );
});

OfferImages.displayName = 'OfferImages';

export default function EditEldoradoOfferPage() {
  const { offerId } = useParams<{ offerId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [currency, setCurrency] = useState("USD");
  const [isSaving, setIsSaving] = useState(false);
  const [offerData, setOfferData] = useState<OfferEditDetailsResponse | null>(null);
  const [accountDetails, setAccountDetails] = useState<string[]>([]);
  const [mainImage, setMainImage] = useState<OfferImage | null>(null);
  const [offerImages, setOfferImages] = useState<OfferImage[]>([]);

  // Handle image upload success
  const handleImageUploadSuccess = (uploadedImages: OfferImage[]) => {
    if (uploadedImages.length > 0) {
      const newImage = uploadedImages[0];
      if (!mainImage) {
        // Set as main image if no main image exists
        setMainImage(newImage);
        toast.success("Image uploaded and set as main image!");
      } else {
        // Add to additional images
        setOfferImages(prev => [...prev, newImage]);
        toast.success("Image uploaded successfully!");
      }
    }
  };

  // Handle image upload error
  const handleImageUploadError = (error: Error) => {
    console.error("Image upload error:", error);
    toast.error(`Failed to upload image: ${error.message}`);
  };

  // Fetch offer details
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["eldorado-offer-details", offerId],
    queryFn: async () => {
      if (!offerId) throw new Error("No offer ID provided");
      return await eldoradoOffersService.getOfferEditDetails(offerId);
    },
    enabled: !!offerId,
  });

  // Fetch offer account details
  const { data: accountDetailsData, isLoading: isAccountDetailsLoading } = useQuery({
    queryKey: ["eldorado-offer-account-details", offerId],
    queryFn: async () => {
      if (!offerId) throw new Error("No offer ID provided");
      return await eldoradoOffersService.getOfferAccountDetails(offerId);
    },
    enabled: !!offerId,
  });

  // Update local state when data is loaded
  useEffect(() => {
    if (data) {
      setOfferData(data);
      setTitle(data.offer.offerTitle);
      setDescription(data.offer.description);
      setPrice(data.offer.pricePerUnit.amount.toString());
      setCurrency(data.offer.pricePerUnit.currency);
      setMainImage(data.offer.mainOfferImage);
      setOfferImages(data.offer.offerImages);
    }
  }, [data]);

  // Update account details when loaded
  useEffect(() => {
    if (accountDetailsData) {
      const details = accountDetailsData.accountsDetails.map(account => account.secretDetails);
      setAccountDetails(details.length > 0 ? details : [""]);
    }
  }, [accountDetailsData]);

  // Handle navigation if no offer ID
  useEffect(() => {
    if (!offerId) {
      toast.error("No offer ID provided.");
      navigate("/");
    }
  }, [offerId, navigate]);

  const handleSave = async () => {
    if (!title.trim() || !description.trim()) {
      toast.error("Please fill in both title and description.");
      return;
    }

    if (accountDetails.some(detail => !detail.trim())) {
      toast.error("Please fill in all account details or remove empty ones.");
      return;
    }

    if (!price.trim() || isNaN(Number(price)) || Number(price) <= 0) {
      toast.error("Please enter a valid price.");
      return;
    }

    if (!offerData || !offerId) {
      toast.error("Offer data not loaded.");
      return;
    }

    setIsSaving(true);
    try {
      // Map the current offer data to update request format
      const updateRequest = mapPrivateOfferToUpdateRequest(offerData, {
        accountsDetails: accountDetails.map(detail => ({ secretDetails: detail })),
        secretDetails: accountDetails.map(detail => ({ secretDetails: detail })),
      });
      
      // Update with new values
      updateRequest.details.offerTitle = title;
      updateRequest.details.description = description;
      updateRequest.details.pricing.pricePerUnit = {
        amount: Number(price),
        currency: currency,
      };
      
      // Update images
      updateRequest.details.mainOfferImage = mainImage;
      updateRequest.details.offerImages = offerImages;

      await eldoradoOffersService.updateOffer(offerId, updateRequest);
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["eldorado-offers"] });
      queryClient.invalidateQueries({ queryKey: ["eldorado-offer-details", offerId] });
      
      toast.success("Offer updated successfully!");
      navigate("/");
    } catch (error) {
      console.error("Failed to update offer:", error);
      toast.error("Failed to update offer. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAndResume = async () => {
    if (!title.trim() || !description.trim()) {
      toast.error("Please fill in both title and description.");
      return;
    }

    if (accountDetails.some(detail => !detail.trim())) {
      toast.error("Please fill in all account details or remove empty ones.");
      return;
    }

    if (!price.trim() || isNaN(Number(price)) || Number(price) <= 0) {
      toast.error("Please enter a valid price.");
      return;
    }

    if (!offerData || !offerId) {
      toast.error("Offer data not loaded.");
      return;
    }

    setIsSaving(true);
    try {
      // First save the offer
      const updateRequest = mapPrivateOfferToUpdateRequest(offerData, {
        accountsDetails: accountDetails.map(detail => ({ secretDetails: detail })),
        secretDetails: accountDetails.map(detail => ({ secretDetails: detail })),
      });
      
      // Update with new values
      updateRequest.details.offerTitle = title;
      updateRequest.details.description = description;
      updateRequest.details.pricing.pricePerUnit = {
        amount: Number(price),
        currency: currency,
      };
      
      // Update images
      updateRequest.details.mainOfferImage = mainImage;
      updateRequest.details.offerImages = offerImages;

      await eldoradoOffersService.updateOffer(offerId, updateRequest);
      
      // Then resume the offer
      await eldoradoOffersService.resumeOffer(offerId);
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["eldorado-offers"] });
      queryClient.invalidateQueries({ queryKey: ["eldorado-offer-details", offerId] });
      
      toast.success("Offer saved and resumed successfully!");
      navigate("/");
    } catch (error) {
      console.error("Failed to save and resume offer:", error);
      toast.error("Failed to save and resume offer. Please try again.");
    } finally {
       setIsSaving(false);
     }
   };

  if (isLoading || isAccountDetailsLoading) {
    return (
      <div className="min-h-screen bg-background p-4 md:p-8 flex items-center justify-center">
        <p>Loading offer details...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-background p-4 md:p-8 flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">Failed to load offer details</p>
          <p className="text-muted-foreground mb-4">{error?.message}</p>
          <Button onClick={() => navigate("/")}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4 md:p-8">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <CardTitle>Edit Eldorado Offer {offerId}</CardTitle>
            <div className="w-10"></div> {/* Spacer */}
          </div>
          <CardDescription>
            Update the details for your Eldorado offer.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
             <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Account Details</Label>
              <div className="flex gap-2">
                <SelectLocalAccountDialog
                  accountTypeFilter="eldorado"
                  onAccountSelected={(account: LocalAccount) => {
                    const formattedDetails = `Login: ${account.email}\nPassword: ${account.password}`;
                    setAccountDetails([...accountDetails, formattedDetails]);
                  }}
                  trigger={
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="gap-1"
                    >
                      <Plus className="h-4 w-4" />
                      Select Account
                    </Button>
                  }
                />
              
              </div>
            </div>
            {accountDetails.map((detail, index) => (
              <div key={index} className="flex gap-2">
                <Textarea
                  value={detail}
                  onChange={(e) => {
                    const newDetails = [...accountDetails];
                    newDetails[index] = e.target.value;
                    setAccountDetails(newDetails);
                  }}
                  placeholder="Enter account details (username, password, etc.)"
                  rows={3}
                  className="resize-y flex-1"
                />
                {accountDetails.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      const newDetails = accountDetails.filter((_, i) => i !== index);
                      setAccountDetails(newDetails);
                    }}
                    className="shrink-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="title">Title</Label>
              <EldoradoFillWithAIButton
                inputText={title}
                onTitleGenerated={setTitle}
                onDescriptionGenerated={setDescription}
              />
            </div>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter offer title"
              className="h-12"
            />
          </div>
       
          <div className="space-y-2">
            <Label htmlFor="price">Price</Label>
            <div className="flex gap-2">
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                placeholder="Enter price"
                className="h-12"
              />
              <Input
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                placeholder="Currency"
                className="h-12 w-24"
                disabled
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter offer description"
              rows={15}
              className="resize-y"
            />
          </div>
          {/* Images Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Images</Label>
              <EldoradoImageUploader
                onUploadSuccess={handleImageUploadSuccess}
                onUploadError={handleImageUploadError}
                disabled={isSaving}
                accountDetails={accountDetails.length > 0 ? accountDetails[0] : undefined}
              />
            </div>
            {(mainImage || offerImages.length > 0) && (
              <OfferImages 
                mainImage={mainImage}
                images={offerImages}
                onDeleteMainImage={() => setMainImage(null)}
                onDeleteImage={(index) => {
                  setOfferImages(prev => prev.filter((_, i) => i !== index));
                }}
              />
            )}
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleSave} disabled={isSaving} className="gap-1">
              <Save className="h-4 w-4" />
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
            <Button onClick={handleSaveAndResume} disabled={isSaving} className="gap-1">
              <Play className="h-4 w-4" />
              {isSaving ? "Saving..." : "Save & Resume"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}