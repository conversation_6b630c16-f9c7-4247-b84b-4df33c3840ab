import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ArrowLeft, ExternalLink, Users, X } from "lucide-react";
import { EldoradoFillWithAIButton } from "@/components/EldoradoFillWithAIButton";
import { SelectLocalAccountDialog } from "@/components/SelectLocalAccountDialog";
import { TitlePresets } from "@/components/TitlePresets";
import { LocalAccount } from "@/lib/db";
import { eldoradoOffersService, mapPrivateOfferToUpdateRequest } from "@/lib/eldorado-api";

export default function CreateEldoradoOfferPage() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedAccount, setSelectedAccount] = useState<LocalAccount | null>(null);



  const handleAccountSelected = (account: LocalAccount) => {
    setSelectedAccount(account);
    toast.success(`Account ${account.email} selected`);
  };

  const clearSelectedAccount = () => {
    setSelectedAccount(null);
    toast.success("Account selection cleared");
  };

  const handleBack = () => {
    // Refresh offers list when navigating back
    queryClient.invalidateQueries({ queryKey: ["offers"] });
    queryClient.invalidateQueries({ queryKey: ["eldorado-offers"] });
    navigate("/");
  };


  const testApi = async () => {
    // eldoradoOffersService.listCurrentOffers({}).then((response) => {
    //   console.log(response);
    // }); 
    
    const details = await eldoradoOffersService.getOfferEditDetails("1a2f8d80-57f6-4b46-a28b-3804f389d626")
    const mapped = mapPrivateOfferToUpdateRequest(details, {
      accountsDetails: [{
        secretDetails: "test 12345"
      }], //TODO From editor
      secretDetails: [], //TODO From editor
    })

    mapped.details.offerTitle = "test 1234 test"

    console.log(mapped)

    //sleep 5 secs
    await new Promise(resolve => setTimeout(resolve, 5000))
    await eldoradoOffersService.updateOffer("1a2f8d80-57f6-4b46-a28b-3804f389d626", mapped)
  }    

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button onClick={testApi}>Test api</Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="mr-4"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl pe-3 font-bold tracking-tight">
                Create New Eldorado Offer
              </h1>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleBack}
              variant="outline"
              size="lg"
              className="rounded-full"
            >
              Cancel
            </Button>
            <Button
              asChild
              size="lg"
              className="rounded-full gap-1"
            >
              <a
                href="https://www.eldorado.gg/sell/find-item?gameId=10&category=Account"
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="h-4 w-4" /> Continue in Eldorado
              </a>
            </Button>
          </div>
        </div>

        <div className="space-y-8">
          {/* Basic Information Card */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                General information about your Eldorado offer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="title">Title</Label>
                  <div className="flex items-center gap-2">
                    <TitlePresets
                      currentTitle={title}
                      onTitleSelect={setTitle}
                    />
                    <EldoradoFillWithAIButton
                      inputText={title}
                      onTitleGenerated={setTitle}
                      onDescriptionGenerated={setDescription}
                    />
                  </div>
                </div>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter offer title"
                  className="h-12"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter offer description"
                  rows={25}
                  className="resize-y"
                />
              </div>
            </CardContent>
          </Card>

          {/* Account Information Card */}
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Select an account for this Eldorado offer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Account Selection</Label>
                <div className="flex gap-2">
                  <SelectLocalAccountDialog
                    accountTypeFilter="eldorado"
                    onAccountSelected={handleAccountSelected}
                    trigger={
                      <Button
                        variant="outline"
                        className="h-12 gap-1.5 whitespace-nowrap"
                      >
                        <Users className="h-4 w-4" />
                        Select Account
                      </Button>
                    }
                  />
                  {selectedAccount && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={clearSelectedAccount}
                      className="h-12 w-12 text-muted-foreground hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {selectedAccount && (
                <div className="bg-muted/30 p-4 rounded-md border border-border">
                  <div className="text-sm">
                    <p>Email: {selectedAccount.email}</p>
                    <p>Password: {selectedAccount.password}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}