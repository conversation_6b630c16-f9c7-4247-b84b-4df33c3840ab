import { useState, useCallback, useEffect } from "react";
import { Upload, AlertCircle } from "lucide-react";
import { eldoradoOffersService, type EldoradoImageUploadResponse, type OfferImage } from "../lib/eldorado-api";
import { cn } from "../lib/utils";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { toast } from "sonner";
import { FileSystemExplorerDialog } from "./FileSystemExplorerDialog";
import { 
  isFileSystemAccessSupported,
  setupAccountsDirectory,
  hasAccountsDirectorySetup
} from "../lib/fileSystemAccess";

interface EldoradoImageUploaderProps {
  onUploadSuccess?: (images: OfferImage[]) => void;
  onUploadError?: (error: Error) => void;
  className?: string;
  disabled?: boolean;
  accountDetails?: string; // Account details containing email and password
}

export function EldoradoImageUploader({
  onUploadSuccess,
  onUploadError,
  className,
  disabled = false,
  accountDetails,
}: EldoradoImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [showFileExplorer, setShowFileExplorer] = useState(false);
  const [hasAccountsDir, setHasAccountsDir] = useState(false);

  // Parse email from account details for folder hint
  const parseEmailFromAccountDetails = (details: string): string | undefined => {
    if (!details) return undefined;
    const emailMatch = details.match(/Email:\s*([^\s\n]+@[^\s\n]+)/i);
    return emailMatch ? emailMatch[1] : undefined;
  };

  const autoNavigateToFolder = parseEmailFromAccountDetails(accountDetails || "")?.split("@")[0];

  useEffect(() => {
    const checkAccountsDirectory = async () => {
      if (isFileSystemAccessSupported()) {
        const hasDir = await hasAccountsDirectorySetup();
        setHasAccountsDir(hasDir);
      }
    };
    checkAccountsDirectory();
  }, []);



  const processFile = async (file: File) => {
    if (disabled) return;
    
    // Validate file type
    if (!file.type.startsWith("image/")) {
      const error = new Error("Only image files are allowed");
      onUploadError?.(error);
      toast.error("Only image files are allowed");
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      const error = new Error("File size must be less than 10MB");
      onUploadError?.(error);
      toast.error("File size must be less than 10MB");
      return;
    }



    // Upload file
    try {
      setIsUploading(true);
      const response: EldoradoImageUploadResponse = await eldoradoOffersService.uploadImage(file);

      if (response.localPaths && response.localPaths.length >= 3) {
        // Convert the response to OfferImage format
        // The API returns 3 paths: Small, Large, Original
        // Remove 'offerimages/' prefix if present to avoid duplication
        const cleanPath = (path: string) => path.replace("/offerimages/", '');
        

        const offerImage: OfferImage = {
          smallImage: cleanPath(response.localPaths[0]), // Small
          largeImage: cleanPath(response.localPaths[1]), // Large
          originalSizeImage: cleanPath(response.localPaths[2]), // Original
        };

        console.log("Uploaded: ", offerImage)
        onUploadSuccess?.([offerImage]);
        toast.success("Image uploaded successfully!");
      } else {
        throw new Error("Invalid response format from server");
      }
    } catch (error) {
      console.error("Eldorado image upload error:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown upload error";
      onUploadError?.(error instanceof Error ? error : new Error(errorMessage));
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setIsUploading(false);
    }
  };



  const handleButtonClick = async () => {
    if (disabled) return;
    
    if (!isFileSystemAccessSupported()) {
      onUploadError?.(new Error("File System Access API is not supported in this browser. Please use a modern browser like Chrome, Edge, or Safari."));
      return;
    }

    // Check if we have directory access, if not, set it up first
    if (!hasAccountsDir) {
      try {
        const success = await setupAccountsDirectory();
        if (success) {
          setHasAccountsDir(true);
          setShowFileExplorer(true);
        }
      } catch (error) {
        console.error('Failed to setup accounts directory:', error);
        onUploadError?.(new Error('Failed to setup accounts directory'));
      }
    } else {
      setShowFileExplorer(true);
    }
  };

  const handleFilesSelected = (files: File[]) => {
    if (files.length > 0) {
      processFile(files[0]);
    }
  };

  return (
    <div className={cn("relative", className)}>
      {!isFileSystemAccessSupported() ? (
        <div className="flex items-center gap-2">
          <AlertCircle className="w-4 h-4 text-destructive" />
          <span className="text-sm text-destructive">
            File System Access API not supported
          </span>
        </div>
      ) : (
        <Button
          variant="outline"
          size="sm"
          onClick={handleButtonClick}
          disabled={disabled || isUploading}
          className="gap-2"
        >
          <Upload className="w-4 h-4" />
          {isUploading ? "Uploading..." : "Upload Image"}
        </Button>
      )}

      <FileSystemExplorerDialog
        open={showFileExplorer}
        onOpenChange={setShowFileExplorer}
        onFilesSelected={handleFilesSelected}
        accept={{
          'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg']
        }}
        multiple={false}
        defaultPath="C:\Users\<USER>\Desktop\GoldfarmData\accs_sale\accounts"
        autoNavigateToFolder={autoNavigateToFolder}
        title="Select Image for Eldorado"
      />
    </div>
  );
}