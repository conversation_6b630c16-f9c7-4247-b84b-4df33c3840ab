/**
 * Eldorado Auth service for managing Eldorado authentication with refresh tokens
 */

const ELDORADO_REFRESH_TOKEN_KEY = "eldoradoRefreshToken";
const ELDORADO_ID_TOKEN_KEY = "eldoradoIdToken";

export const eldoradoAuthService = {
  /**
   * Set the Eldorado refresh token
   * @param refreshToken The Eldorado refresh token to store
   */
  setRefreshToken: (refreshToken: string): void => {
    localStorage.setItem(ELDORADO_REFRESH_TOKEN_KEY, refreshToken);
  },

  /**
   * Get the current Eldorado refresh token
   * @returns The current Eldorado refresh token or null if not authenticated
   */
  getRefreshToken: (): string | null => {
    return localStorage.getItem(ELDORADO_REFRESH_TOKEN_KEY);
  },

  /**
   * Set the Eldorado ID token (from refresh token response)
   * @param idToken The Eldorado ID token to store
   */
  setIdToken: (idToken: string): void => {
    localStorage.setItem(ELDORADO_ID_TOKEN_KEY, idToken);
  },

  /**
   * Get the current Eldorado ID token
   * @returns The current Eldorado ID token or null if not available
   */
  getIdToken: (): string | null => {
    return localStorage.getItem(ELDORADO_ID_TOKEN_KEY);
  },

  /**
   * Check if the user is currently authenticated with Eldorado
   * @returns True if authenticated, false otherwise
   */
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem(ELDORADO_REFRESH_TOKEN_KEY);
  },

  /**
   * Remove all Eldorado authentication tokens (logout)
   */
  clearTokens: (): void => {
    localStorage.removeItem(ELDORADO_REFRESH_TOKEN_KEY);
    localStorage.removeItem(ELDORADO_ID_TOKEN_KEY);
  },

  /**
   * Refresh the ID token using the stored refresh token
   * @returns Promise with the new ID token or throws error
   */
  refreshIdToken: async (): Promise<string> => {
    const refreshToken = eldoradoAuthService.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch('/api/eldorado/authentication/refreshTokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-XSRF-Token': 'd0760bfcb9a38dfe2698590b35a5612e950e7354cf58d33d45e1afff2fe9253f',
          // 'XX-Cookie': `__Host-EldoradoRefreshToken=${refreshToken};__Host-XSRF-TOKEN=d0760bfcb9a38dfe2698590b35a5612e950e7354cf58d33d45e1afff2fe9253f`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to refresh token: ${response.status}`);
      }

      
      console.log("XX-Set-Cookie header:", response.headers.get('xx-set-cookie'));
      
      const responseText = await response.text();
      console.log("Response text:", responseText);
  
      const idToken = response.headers.get('xx-set-cookie');
      if (idToken) {
        eldoradoAuthService.setIdToken(idToken);
        return idToken;
      }

      throw new Error('ID token not found in response');
    } catch (error) {
      console.error('Failed to refresh ID token:', error);
      throw error;
    }
  },

  // Legacy methods for backward compatibility
  setCookie: (xxCookie: string): void => {
    // For backward compatibility, treat as refresh token
    eldoradoAuthService.setRefreshToken(xxCookie);
  },

  getCookie: (): string | null => {
    return `${eldoradoAuthService.getIdToken()};__Host-XSRF-TOKEN=d0760bfcb9a38dfe2698590b35a5612e950e7354cf58d33d45e1afff2fe9253f`
  },

  clearCookie: (): void => {
    eldoradoAuthService.clearTokens();
  },
};