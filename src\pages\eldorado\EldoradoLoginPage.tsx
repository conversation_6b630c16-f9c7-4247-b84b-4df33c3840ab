import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Key, LogIn } from "lucide-react";
import { eldoradoAuthService } from "@/lib/eldorado-auth";

export default function EldoradoLoginPage() {
  const [refreshToken, setRefreshToken] = useState(
    "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.sGpigVsQaavDT8ByRX4uoNO-cdkRG_dF2D7fXzMIxGasatCeCRGwv-RqSZGVN6I1bgpMFZ9XDT4-musgU20-G867OhbWJAL3tdbSCdKRJvlYbiYucgILVmxF2wSXtlvieccNVqVYVu2muNmbSoHk3AsDlP2XsAcbl0GnEqLsF34zIGEY54PhU_6xF8SAlq-_oeEhQa5RDBKKp7bTGpfwEsZ3k1RkrIS6H8fud3-99GiSRjTH_VV0YLS9hXBAaK_7DuA92aDtC6iR_GUw5rldzL6-0N0JSEZGzOemX3p0NfQk1dDej9lkgr2g-N29uTewcxDsT6U41agjm-ofoD-vwQ.slO26ic5dGqTljVk.PHvLQZw-HyU1WdBROj06ErWcQIUpyQY-Gp7As3unCQ3Ms6VOo_aQn790BVAz8_wCAuDaRNe7dGNZLx039GQdI2--6bOrwLvKib2dci1Y4vlIbWOwM50NmJ5fkux1w1QWlZPYeDN0iaXcH-o2bgG75EUDys85na_dYPDyq8TWduay0cjE52AGQi1BiApSkMIHlp1I6tE5gRnIiZ_UX8mltw8j4xKWOxQ2_6pGpsnJHs0cX5wJPM7zLZqUAlwMmJcQBnCIewsqBVD7VBBtb-miIrtYAiYpJpyKPU8aMDHuOibswm7lC_Bz9sOrZfjf-9QHE-OP22PLv23jed4_XW1CT2x_xy2CXGghMtiX6wJZ1skVFlh_AwQ43za8ighJ2bSaf2PQQKbcMgUmldf8NVm1Pz3IaKMULg0175WQaCSMxgL0mS6eF-pnCn_UfWOxRFwuwcHRtRzBagGtGBxBMvtj6JCsO8MQc0yRBK_y-RONEldjmw8RYxkdYU_N_93kvbO0fwiCKMruN4FB5tFSj2fpzgKCTACOm0emYHkH_K1Es_K40jhBqnUNNpy628kaJVbSEdbHnzesQm_a0dP89raQuNdkMMXK7YOs2yi-E4yznz903NDKEg6I5KJA1nZiiaBGp0il4P_dOVHWRWXB5gDcv8VUgrVcCj9zD1uVVIj6KbR_6vn_qntHDBnuqunKEbQQJ2yF2fTNhIfHK9XPzatd_3-5UKRoDgQRGTFVJsxSjVuiada_rI9AaaqfvkrrS8BLM3en4UuZeFyGkuwCTfBvH2qgIunxeXLbwGPACLaEsg7TMC4hNLmXzdKaMOdn3ZLirbFBrKMUfK17yoj6Dcz23AvmQJge7xFyQxbubrWKuFLawmCgRJLtcDnsFjMfsIR_v99nMoNVgdNOjbz7M62nA7g8-QYvaaMRiBSD0LiP9f7QpynUTREiFBa4fhuE_2T2XCrzt6S-dhELyx2IgO3efvYSqcpSp1Wo7qWtSfroHFlko8ELb234zi4y_9OjyFEoLQSl-VVbTawKLkE_cfe49Ejx9DB2SR9uuN34Lo9LtJDw92V6nKYMrmxHjBVxPUzU5MaV21C49XAUfh59vkhRYBy3DdgkZeFPmtwRi6lSppaXrZnsQroTyzeX1RNhcF44lBTFWNASSgoGGixCGjhZGTIn0ZfWAQQK3-bw3JAYyyUpPrMsE0QBfwrjUlVDq9ncRSJZLHgDQTVxQIh1LGV9fC4CSYRhHN_BOIv5mIcJrNBHKFkJpJUbZ0nF_-qL_exFriAe5-GBjcIyQw.xmmNG2-FhhzvWmsDKyv5SQ"
  );
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Parse URL query parameters on mount
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const urlRefreshToken = queryParams.get("refreshToken");

    if (urlRefreshToken) {
      setRefreshToken(urlRefreshToken);
      // Auto-continue if refresh token is provided via URL
      handleLogin(urlRefreshToken);
    }
  }, [location, navigate]);

  const handleLogin = async (tokenToUse = refreshToken) => {
    if (!tokenToUse.trim()) {
      return; // Don't proceed if refresh token is empty
    }

    setIsLoading(true);

    try {
      // Store refresh token using Eldorado auth service
      eldoradoAuthService.setRefreshToken(tokenToUse);

      // Get ID token using the refresh token
      await eldoradoAuthService.refreshIdToken();

      // Check if we have a redirect URL
      const queryParams = new URLSearchParams(location.search);
      const redirectTo = queryParams.get("redirectTo") || "/";

      // Redirect after successful login
      navigate(redirectTo);
    } catch (error) {
      console.error("Eldorado login failed:", error);
      // You could add error handling/messaging here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-border/40 shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Eldorado Authentication</CardTitle>
            <CardDescription className="text-muted-foreground">
              Enter your Eldorado refresh token to authenticate with the Eldorado API
            </CardDescription>
          </CardHeader>

          <CardContent>
            <div className="relative">
              <div className="absolute top-3 left-3 pointer-events-none text-muted-foreground">
                <Key className="h-4 w-4" />
              </div>
              <Textarea
                placeholder="Paste your Eldorado refresh token here..."
                value={refreshToken}
                onChange={(e) => setRefreshToken(e.target.value)}
                className="pl-10 min-h-[120px] resize-none"
                rows={6}
              />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              <p>This should be the refresh token from your Eldorado authentication.</p>
              <p>The system will automatically exchange this for an ID token.</p>
            </div>
          </CardContent>

          <CardFooter>
            <Button
              className="w-full"
              onClick={() => handleLogin()}
              disabled={isLoading || !refreshToken.trim()}
            >
              {isLoading ? (
                <span className="flex items-center gap-1">
                  <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Processing...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <LogIn className="h-4 w-4" />
                  Authenticate with Eldorado
                </span>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}