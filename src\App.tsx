import { Routes, Route } from "react-router-dom";
import MainPage from "@/pages/MainPage";
import OfferDetailPage from "@/pages/offers/[offerId]";
import CreateOfferPage from "@/pages/offers/create";
import CreateEldoradoOfferPage from "@/pages/eldorado/CreateEldoradoOfferPage"; // Added
import EditEldoradoOfferPage from "@/pages/eldorado/EditEldoradoOfferPage"; // Added
import EldoradoLoginPage from "@/pages/eldorado/EldoradoLoginPage"; // Added
import LoginPage from "@/pages/LoginPage";

import { Toaster } from "@/components/ui/sonner";

function App() {
  return (
    <>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/eldorado/login" element={<EldoradoLoginPage />} /> {/* Added */}
        <Route path="/" element={<MainPage />} />
        <Route path="/offers/:offerId" element={<OfferDetailPage />} />
        <Route path="/offers/create" element={<CreateOfferPage />} />
        <Route path="/eldorado/offers/create" element={<CreateEldoradoOfferPage />} /> {/* Added */}
        <Route path="/eldorado/offers/:offerId/edit" element={<EditEldoradoOfferPage />} /> {/* Added */}
      </Routes>
      <Toaster />
    </>
  );
}

export default App;
